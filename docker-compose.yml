version: '3.8'

services:
  directus:
    container_name: directustemushop
    image: directus/directus:latest
    ports:
      - 8055:8055
    environment:
      KEY: '255d861b-5ea1-5996-9aa3-922530ec40b1'
      SECRET: '6116487b-cda1-52c2-b5b5-c8022c45e263'

      # 时区
      TZ: 'Asia/Shanghai'

      # 数据库配置
      DB_CLIENT: 'mysql'
      DB_HOST: '**********'
      DB_PORT: '3306'
      DB_DATABASE: 'dtstemushop'
      DB_USER: 'dtstemushop'
      DB_PASSWORD: 'BKeFSsXNry7tMkfj'

      # 管理员账户
      ADMIN_EMAIL: '<EMAIL>'
      ADMIN_PASSWORD: 'Zsj868822'

      # 存储配置
      STORAGE_LOCATIONS: 'local'
      STORAGE_LOCAL_DRIVER: 'local'
      STORAGE_LOCAL_ROOT: './uploads'

      # 文件上传
      FILES_MAX_UPLOAD_SIZE: '100MB'
      FILES_MIME_TYPE_ALLOW_LIST: 'image/jpeg,image/png,image/gif,image/webp,application/pdf'

      # CORS 跨域配置 - 支持全部域名
      CORS_ENABLED: 'true'
      CORS_ORIGIN: '*'
      CORS_METHODS: 'GET,POST,PATCH,DELETE,PUT,OPTIONS,HEAD'
      CORS_ALLOWED_HEADERS: 'Content-Type,Authorization,Cache-Control,X-Requested-With,Accept,Origin,X-Directus-*'
      CORS_EXPOSED_HEADERS: 'Content-Range,X-Total-Count,Content-Disposition'
      CORS_CREDENTIALS: 'false'
      CORS_MAX_AGE: '18000'

      # 内容安全策略
      CONTENT_SECURITY_POLICY_DIRECTIVES__CONNECT_SRC: "'self' http: https: ws: wss: data: blob:"
      CONTENT_SECURITY_POLICY_DIRECTIVES__DEFAULT_SRC: "'self' http: https: data: blob:"
      CONTENT_SECURITY_POLICY_DIRECTIVES__IMG_SRC: "'self' http: https: data: blob:"

      # Extensions 热更新
      EXTENSIONS_AUTO_RELOAD: 'true'

    volumes:
      - ./uploads:/directus/uploads
      - ./extensions:/directus/extensions
    depends_on:
      - redis
    network_mode: bridge

  redis:
    container_name: directustemushop-redis
    image: redis:6-alpine
    environment:
      TZ: 'Asia/Shanghai'
    network_mode: bridge
